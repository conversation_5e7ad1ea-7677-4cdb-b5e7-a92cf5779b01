import time
import logging
import datetime
from core.context import context_manager
from core.setting import SETTINGS
from strategy.跟风买入策略 import FollowTheLeaderStrategy
qmt_trader = context_manager.get_obj("qmt_trader")
qmt_data = context_manager.get_obj("qmt_data")
cash_manager = context_manager.get_obj("cash_manager")
cash_manager.on_init()

qmt_trader.connect(SETTINGS)


# 订阅
qmt_data.subscribe(cash_manager.code_list)
time.sleep(30)
# 启动行情缓存
qmt_data.add_queue(cash_manager.queue, cash_manager.name)
cash_manager.start()


# 策略启动 跟风策略
st = FollowTheLeaderStrategy()
st.stock_pool = cash_manager.code_list
cash_manager.add_queue(st.queue, st.name)
st.start()

vt_symbols = cash_manager.code_list[0]
cash_tick = cash_manager.vt_symbols[vt_symbols]
while True:
    if cash_tick.values:
        print(f"运行中..., 当前最新数据的时间: {cash_tick.tick.datetime}")
    time.sleep(30)
    