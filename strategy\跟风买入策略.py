from core.cash_data import CashManager
from core.utility import datatime_to_hms_int
import datetime
from threading import Thread
from queue import Queue, Empty

from .base_strategy import BaseStrategy, TickData

class FollowTheLeaderStrategy(BaseStrategy):
    name = "FollowTheLeaderStrategy"
    def __init__(self) -> None:
        super().__init__()
        self.queue = Queue()
        self.active = False
        self.thread = Thread(target=self.on_tick)
        # 前四笔策略配置
        self.four_pen_config = {
            "max_turnover_rate": 2.0,  # 最大换手率阈值
            "limit_up_turnover_rate": 1.0,  # 涨停价连续N笔委托买一量换手率阈值
            "N": 8,  # 资金N等分
            "amount_per_part": 0,  # 每等分的金额, 每天初始化自动计算

        }
        # 5笔到N分钟策略配置
        self.five_to_n_config = {
            "time_window": 3,  # N分钟时间窗口
            "rise_threshold": 3.0,  # 涨幅阈值X%
            "max_single_turnover": 2.0,  # 单笔最大换手率Y%
            "delay_ticks": 2,  # 延时观察tick数
            "N": 10,  # 资金N等分
            "amount_per_part": 0,  # 每等分的金额, 每天初始化自动计算
        }

        # 撤单配置
        self.cancel_config = {
            "no_limit_up_time": "150000",  # 未再次封板撤单时间
            "turnover_threshold_x": 2.0,  # 撤单换手率阈值X%
            "consecutive_threshold_y": 1.0,  # 连续2笔换手率阈值y%
            "vol_x": 0.5,  # 委托买一量比例阈值
            "amount_x": 10,  # 委托买一额阈值X万元
        }
        # 股票池
        self.stock_pool = []
        self.buyed_stocks = []
        self.buyed_two = []

    def on_init(self):
        signal_amount = self.qmt_trader.balance / self.four_pen_config["N"]
        if signal_amount > 200 * 10000:
            self.four_pen_config["amount_per_part"] = 100 * 10000
        self.four_pen_config["amount_per_part"] = signal_amount

    def start(self):
        self.on_init()
        self.active = True
        self.thread.start()

    
    # 前四笔策略
    def four_pen_strategy(self, tick: TickData):
        n = 4
        cash_tick = self.cash_ticks[tick.vt_symbol]
        if cash_tick.up_stop_index:
            count = 0
            first_up_idx = cash_tick.up_stop_index[0]
            for idx in range(first_up_idx + 1, n):
                tick  = cash_tick.get_index_tick(idx)
                turnover = tick.net_volume / cash_tick.float_volume * 100
                if turnover >= self.four_pen_config["max_turnover_rate"]:
                    count += 1
            if count == 4:
                self.buy_4(tick)
                return True
            return False
        cash_tick = self.cash_ticks[tick.vt_symbol]
        t0, t1, t2, t3, t4 = cash_tick.values[:5]
        if t1.last_price < t0.last_price:
            if t2.last_price > t0.last_price:
                pass
            else:
                return False
        elif t1.last_price < t0.last_price and t2.last_price < t0.last_price:
            if t3.last_price > t0.last_price and t4.last_price > t0.last_price:
                pass
            else:
                return False
        # 最大换手率
        max_turnover = max([t1.net_volume, t2.net_volume, t3.net_volume, t4.net_volume]) / cash_tick.float_volume * 100
        max_amount = max([t1.net_amount, t2.net_amount, t3.net_amount, t4.net_amount])        
        if max_turnover > self.four_pen_config["max_turnover_rate"] and max_amount > self.four_pen_config["max_amount"]:
            self.buy_4(tick)
            return True
        return False
    
    def buy_4(self, tick: TickData):
        print(f"{tick.vt_symbol} four_pen_strategy 买入一笔 {tick.datetime}")
        # 买入
        amount = self.four_pen_config["amount_per_part"]
        volume = int(amount / tick.last_price / 100) * 100
        self.buy(tick.vt_symbol, volume, tick.last_price, "four_pen_strategy")
        self.buyed_stocks.append(tick.vt_symbol)

    def buy_5_n(self, tick: TickData):
        amount = self.four_pen_config["amount_per_part"]
        volume = int(amount / tick.last_price / 100) * 100
        print(f"时间{tick.datetime } {tick.vt_symbol} 5笔到N分钟策略触发 买入一笔, {amount} {tick.last_price}")
        self.buy(tick.vt_symbol, volume, tick.last_price, "four_pen_strategy")
        self.buyed_stocks.append(tick.vt_symbol)

    def five_to_n_strategy(self, tick: TickData):
        x = 3
        cash_tick = self.cash_ticks[tick.vt_symbol]
        t0 = cash_tick.get_index_tick(0)
        if (tick.change - t0.change ) > x or tick.last_price > (cash_tick.limit_up_price-0.05):
            max_turnover = max([t.net_volume for t in cash_tick.values[5:tick.index]]) / cash_tick.float_volume * 100
            if max_turnover > self.five_to_n_config["max_single_turnover"]:
                # 延迟观察没有做
                
                self.buy_5_n(tick)
    
    # 成交股票监测
    def check_deal_stock(self, tick: TickData):
        x = 3
        y = 5
        if tick.vt_symbol in self.buyed_two:
            return
        cash_tick = self.cash_ticks[tick.vt_symbol]
        pre_low = cash_tick.pre_low
        t0 = cash_tick.get_index_tick(0)
        if (t0.change - tick.change) < x and tick.high / tick.last_price < y and tick.last_price > pre_low and tick.yang_volume > tick.yin_volume:
            if tick.high > tick.open:
                self.buy_5_n(tick)
                self.buyed_two.append(tick.vt_symbol)

    # 人工挂断未成交监控 5 s 监测一次
    def check_hang_up(self):
        self.qmt_trader.query_stock_positions()
        for pos in self.qmt_trader.query_stock_orders():
            # 排除策略下单，但是策略下单必须指定策略名称 否则认为是非策略下单
            if not pos.strategy_name:
                continue
            if pos.order_status in (51, 53, 54, 56, 57, 255):
                continue
            vt_symbol = pos.vt_symbol
            cash_tick = self.cash_ticks[vt_symbol]
            ticks = cash_tick.get_start_ticks(pos.order_time, -1)
            y_count = 0
            for tick in ticks:
                turnover = tick.net_volume / cash_tick.float_volume * 100
                if turnover > self.cancel_config["turnover_threshold_x"]:
                    self.cancel_order(pos.order_id)
                    break
                if (tick.bid_volume_1 - tick.max_bid_price_1) / tick.max_bid_price_1 * 100> self.cancel_config["vol_x"]:
                    if tick.bid_amount_1 > self.cancel_config["amount_x"] * 10000:
                        self.cancel_order(pos.order_id)
                        break
                if turnover > self.cancel_config["consecutive_threshold_y"]:
                    y_count += 1
                    if y_count >= 2:
                        self.cancel_order(pos.order_id)
                        break
                else:
                    y_count = 0

    def cancel_order(self, order_id):
        print(f"取消订单 {order_id}")
        # self.qmt_trader.cancel_order(order_id)

    def on_tick(self):
        """tick数据处理主循环"""
        last_timestamp = 0
        dely_time = 5
        while self.active:
            try:
                tick:TickData = self.queue.get(timeout=0.5)
                # if tick.timestamp - last_timestamp > dely_time:
                #     self.check_deal_stock(tick)
                #     last_timestamp = tick.timestamp
                #     continue
                if tick.vt_symbol not in self.stock_pool:
                    continue
                print(tick.index)
                # if tick.vt_symbol in self.buyed_stocks:
                #     continue
                # if tick.index == 4:
                #     self.four_pen_strategy(tick)
                # elif tick.index < 5:
                #     continue
                # if tick.vt_symbol in self.buyed_stocks:
                #     continue
                # if tick.timestamp < self.five_to_n_config["time_window"] * 60 * 1000:
                #     if not self.five_to_n_config["amount_per_part"]:
                #        self.five_to_n_config["amount_per_part"] = self.qmt_trader.balance / self.five_to_n_config["N"] 
                #     self.five_to_n_strategy(tick)
                if tick.index == 10:
                    print(tick)
                    start_time = "093000"
                    end_time = "093100"
                    # print("f01", self.f01(tick.vt_symbol))
                    # print("f01b", self.f01b(tick.vt_symbol))
                    # print("f02", self.f02(tick.vt_symbol))
                    # print("f03", self.f03(tick.vt_symbol))
                    # print("f04", self.f04(tick.vt_symbol))
                    # print("f05", self.f05(tick.vt_symbol))
                    # print("f06", self.f06(tick.vt_symbol))
                    # print("f07", self.f07(tick.vt_symbol))
                    # print("f08", self.f08(tick.vt_symbol))
                    # print("f09", self.f09(tick.vt_symbol))
                    # print("f10", self.f10(tick.vt_symbol))
                    # print("f11", self.f11(tick.vt_symbol))
                    # print("f12", self.f12(tick.vt_symbol))
                    # print("f13", self.f13(tick.vt_symbol))
                    # print("f14", self.f14(tick.vt_symbol))
                    print("f15b", self.f15b(tick.vt_symbol))
                    print("f16a", self.f16a(tick.vt_symbol, start_time, end_time))
                    print("f16b", self.f16b(tick.vt_symbol, start_time, end_time))
                    print("f16c", self.f16c(tick.vt_symbol, start_time, end_time))
                    print("f16d", self.f16d(tick.vt_symbol, start_time, end_time))
                    print("f17", self.f17(tick.vt_symbol, start_time, end_time))
                    print("f18", self.f18(tick.vt_symbol, start_time, end_time))
                    print("f19", self.f19(tick.vt_symbol, start_time, end_time))
                    print("f20", self.f20(tick.vt_symbol, start_time, end_time, n=1.6))

            
            except Empty:
                continue
            
