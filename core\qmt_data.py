from datetime import datetime
from queue import Empty, Queue
import pandas as pd
from xtquant import xtdata
import logging
from .object import TickData

logger = logging.getLogger(__name__)


def get_stock_ratio(stock_code):
    """
    根据股票代码获取股票类型。
    简化版：实际应用中需要更精确的方法来确定股票类型。
    """
    if stock_code.startswith(('300', '301')):  # 创业板
        return 0.20
    elif stock_code.startswith('688'):  # 科创板
        return 0.20
    else:  # 主板等其他情况
        return 0.10


class QmtData:
    _cache = {
        "is_run": None,
        "contract": {},
    }
    active = False
    name = 'QmtData'
    _instance = None
    instance = False

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(QmtData, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self.instance:
            return
        self.queue_dct: dict[str, Queue] = {}
        self.code_list = []
        self.req = None
        self.xtdata = xtdata
        # 批量获取最新tick快照
        self.get_full_tick = xtdata.get_full_tick
        # 单个等待下载
        self.download_history_data = xtdata.download_history_data
        # 批量异步下载不等式 需要自己设置阻塞否则下载中断
        self.download_history_data2 = xtdata.download_history_data2
        # 返回字典格式 {open/time...: df_data}
        self.get_market_data = xtdata.get_market_data
        # 返回字典格式 {code: df_data}
        self.get_market_data_ex = xtdata.get_market_data_ex
        # 返回数组格式
        self.get_market_data_ori = xtdata.get_market_data_ori
        # 获取板块信息 行业板块 概念板块...
        self.get_sector_list = xtdata.get_sector_list
        self.get_stock_list_in_sector = xtdata.get_stock_list_in_sector
        self.download_sector_data = xtdata.download_sector_data
        # 获取合约信息，流通股数等
        # xtdata.get_instrument_detail
        self.get_instrument_detail = xtdata.get_instrument_detail
        self.get_stock_ratio = get_stock_ratio
        self.instance = True
        self.stock_df: pd.DataFrame = None

    
    def add_queue(self, queue: Queue, key: str):
        if key in self.queue_dct:
            return
        self.queue_dct[key] = queue

    def remove_queue(self, key: str):
        if key in self.queue_dct:
            del self.queue_dct[key]

    def get_stock_contract(self, stock_code):
        if stock_code in self._cache["contract"]:
            return self._cache["contract"][stock_code]
        contract = xtdata.get_instrument_detail(stock_code)
        self._cache["contract"][stock_code] = contract
        return contract

    def add_code_list(self, code_list):
        self.code_list.extend(code_list)
        self.code_list = list(set(self.code_list))

    def get_stock_detail(self, stock_list):
        self.download_sector_data()
        stock_info = {}
        for x in stock_list:
            d = self.get_instrument_detail(x)
            stock_info[x] = {
                "name": d['InstrumentName'],
                "float_volume": d['FloatVolume'],
                "float_amount": round(d['FloatVolume'] * d['PreClose'], 2) / 10 ** 8,
                "total_volume": d['TotalVolume'],
                "ratio": self.get_stock_ratio(x),
            }

        return stock_info

    def get_vt_symbols(self):
        xt_symbols: list[str] = []
        markets: list = [
            "沪深A股",
            # "沪深转债",
            # "沪深ETF",
            # "沪深指数",
            "京市A股"
        ]

        for i in markets:
            names: list = xtdata.get_stock_list_in_sector(i)
            xt_symbols.extend(names)
        return xt_symbols

    def on_init(self):
        if not self.code_list:
            self.code_list = self.get_stock_list_in_sector("沪深A股")

    def subscribe(self, code_list: list[str]=None):
        if self.active:
            logger.warning('不要重复订阅')
            return
        logger.info(f'{self.name} 开始启动')
        if not code_list:
            if not self.code_list:
                self.code_list = self.get_vt_symbols()
            code_list = self.code_list
        else:
            self.code_list = code_list
        req = xtdata.subscribe_whole_quote(
            code_list=code_list,
            callback=self.onMarketData)
        self.active = True
        logger.info(f'{self.name}订阅完成')
        return req

    def stop(self):
        if not self.active:
            return
        logger.info(f'{self.name} 开始停止')
        xtdata.unsubscribe_quote(self.req)
        self.active = False
        self.req = None
        logger.info(f'{self.name} 停止完成')

    def onMarketData(self, data):
        """行情推送回调"""
        ticks = []
        for vt_symbol, d in data.items():
            time_stamp = d["time"]
            bp_data: list = d["bidPrice"]
            ap_data: list = d["askPrice"]
            bv_data: list = d["bidVol"]
            av_data: list = d["askVol"]
            dt = datetime.fromtimestamp(time_stamp / 1000)
            tick: TickData = TickData(
                vt_symbol=vt_symbol,
                datetime=dt,
                volume=d["volume"],
                amount=d["amount"],
                open_interest=d["openInt"],
                last_price=round(d["lastPrice"], 3),
                open=round(d["open"], 3),
                high=round(d["high"], 3),
                low=round(d["low"], 3),
                pre_close=round(d["lastClose"], 3),
                transaction_num=d["transactionNum"],
                bid_price_1=round(bp_data[0], 3),
                bid_price_2=round(bp_data[1], 3),
                bid_price_3=round(bp_data[2], 3),
                bid_price_4=round(bp_data[3], 3),
                bid_price_5=round(bp_data[4], 3),
                ask_price_1=round(ap_data[0], 3),
                ask_price_2=round(ap_data[1], 3),
                ask_price_3=round(ap_data[2], 3),
                ask_price_4=round(ap_data[3], 3),
                ask_price_5=round(ap_data[4], 3),
                bid_volume_1=bv_data[0],
                bid_volume_2=bv_data[1],
                bid_volume_3=bv_data[2],
                bid_volume_4=bv_data[3],
                bid_volume_5=bv_data[4],
                ask_volume_1=av_data[0],
                ask_volume_2=av_data[1],
                ask_volume_3=av_data[2],
                ask_volume_4=av_data[3],
                ask_volume_5=av_data[4],
            )
            ticks.append(tick)

        for queue in self.queue_dct.values():
            queue.put(ticks)

    @property
    def today(self):
        today = datetime.now()
        return today.strftime('%Y%m%d')

    def run(self):
        if not self._cache.get('is_run'):
            xtdata.run()


if __name__ == '__main__':
    code_list = ["002527.SZ", "600238.SH"]
    r = xtdata.get_market_data(stock_list=code_list, count=5)
    # r = xtdata.get_main_contract(code_market=code_list[1],start_time='20250312')
    # r = xtdata.get_full_kline(stock_list=code_list, period='1m', count=10)
    print(r)
