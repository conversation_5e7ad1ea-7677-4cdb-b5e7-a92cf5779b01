import time
import logging
import threading 
import datetime
from core.context import context_manager
from core.setting import SETTINGS
from strategy.跟风买入策略 import FollowTheLeaderStrategy
qmt_trader = context_manager.get_obj("qmt_trader")
qmt_data = context_manager.get_obj("qmt_data")
cash_manager = context_manager.get_obj("cash_manager")
cash_manager.on_init()

taos_client = context_manager.get_obj("taos_client")
qmt_trader.connect(SETTINGS)

st = FollowTheLeaderStrategy()
st.stock_pool = cash_manager.code_list[3:5]
cash_manager.add_queue(st.queue, st.name)
st.start()
taos_client.connect(SETTINGS)
topic_name = "tick"
# 指定 年月日
time.sleep(3)
start_time = datetime.datetime(year=2025, month=8, day=13, hour=9, minute=25, second=0)
end_time = datetime.datetime(year=2025, month=8, day=13, hour=9, minute=32, second=0)
# 启动行情缓存
taos_client.add_queue(cash_manager.queue, cash_manager.name)
cash_manager.start()
thread = threading.Thread(target=taos_client.subscribe_featch, args=(start_time, end_time))
thread.start()



vt_symbols = st.stock_pool[0]
cash_tick = cash_manager.vt_symbols[vt_symbols]
while True:
    print("--------------------------------------------")
    if cash_tick.values:
        print(cash_tick.index)
    print("===============================================")
    time.sleep(30)
    