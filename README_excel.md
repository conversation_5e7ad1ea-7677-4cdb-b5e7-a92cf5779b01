# Excel工具模块使用说明

## 概述

`excel.py` 是一个基于pandas的综合Excel工具模块，提供了完整的Excel文件读取、写入和操作功能，具有强大的错误处理、数据验证和高级功能。

## 依赖要求

在使用此模块之前，请确保安装以下Python包：

```bash
pip install pandas openpyxl numpy
```

## 主要功能

### 1. 读取Excel文件

```python
import excel

# 简单读取
df = excel.read_excel('data.xlsx', sheet='Sheet1')

# 高级读取选项
df = excel.read_excel(
    'data.xlsx', 
    sheet=0, 
    header=1, 
    usecols='A:D',
    skiprows=2,
    nrows=100
)

# 读取多个工作表
all_sheets = excel.read_multiple_excel_sheets('data.xlsx')
```

### 2. 写入Excel文件

```python
import pandas as pd
import excel

# 创建示例数据
df = pd.DataFrame({
    '股票代码': ['000001.SZ', '000002.SZ'],
    '股票名称': ['平安银行', '万科A'],
    '价格': [12.50, 18.30]
})

# 简单写入
excel.write_excel(df, 'output.xlsx', sheet_name='股票数据', index=False)

# 写入多个工作表
data_dict = {
    '股票数据': df,
    '统计信息': df.describe()
}
excel.write_multiple_excel_sheets(data_dict, 'multi_sheet.xlsx')
```

### 3. 数据清理

```python
# 清理Excel数据
clean_df = excel.clean_excel_data(
    df,
    remove_empty_rows=True,
    remove_empty_cols=True,
    strip_strings=True,
    standardize_headers=True
)
```

### 4. 文件信息

```python
# 获取文件信息
info = excel.get_excel_info('data.xlsx')
print(f"文件大小: {info['size_mb']} MB")
print(f"工作表数量: {info['sheet_count']}")
print(f"工作表名称: {info['sheet_names']}")
```

## 高级功能

### ExcelReader类

```python
from excel import ExcelReader

reader = ExcelReader('data.xlsx')

# 获取工作表名称
sheet_names = reader.get_sheet_names()

# 读取指定范围
df_range = reader.read_range(
    sheet='Sheet1',
    start_row=0,
    end_row=10,
    start_col='A',
    end_col='D'
)

# 读取多个工作表
sheets_data = reader.read_multiple_sheets(['Sheet1', 'Sheet2'])
```

### ExcelWriter类

```python
from excel import ExcelWriter

writer = ExcelWriter('output.xlsx', create_backup=True)

# 写入DataFrame
writer.write_dataframe(df, sheet_name='数据', index=False)

# 追加数据到现有工作表
writer.append_to_sheet(new_df, sheet_name='数据')

# 写入多个工作表
writer.write_multiple_sheets({
    'Sheet1': df1,
    'Sheet2': df2
})
```

### ExcelDataProcessor类

```python
from excel import ExcelDataProcessor

processor = ExcelDataProcessor()

# 清理DataFrame
clean_df = processor.clean_dataframe(
    df,
    remove_empty_rows=True,
    strip_strings=True
)

# 转换数据类型
type_mapping = {
    '股票代码': 'str',
    '价格': 'float',
    '日期': 'datetime'
}
converted_df = processor.convert_data_types(df, type_mapping)

# 处理合并单元格
filled_df = processor.handle_merged_cells(df, fill_method='ffill')

# 修复编码问题
fixed_df = processor.detect_and_fix_encoding_issues(df)
```

### ExcelUtilities类

```python
from excel import ExcelUtilities

# 验证文件路径
path = ExcelUtilities.validate_file_path('data.xlsx')

# 获取文件信息
info = ExcelUtilities.get_file_info('data.xlsx')

# 批量处理文件
def process_file(file_path):
    return excel.read_excel(file_path)

results = ExcelUtilities.batch_process_files(
    ['file1.xlsx', 'file2.xlsx'],
    process_file
)
```

## 错误处理

模块使用自定义的`ExcelError`异常类来处理错误：

```python
from excel import ExcelError

try:
    df = excel.read_excel('nonexistent.xlsx')
except ExcelError as e:
    print(f"Excel操作错误: {e}")
except Exception as e:
    print(f"其他错误: {e}")
```

## 示例用法

运行模块中的示例：

```python
import excel

# 运行完整示例
excel.example_usage()
```

或者运行测试脚本：

```bash
python test_excel.py
```

## 注意事项

1. **备份功能**: 默认情况下，写入操作会创建现有文件的备份
2. **内存使用**: 处理大文件时注意内存使用情况
3. **文件格式**: 支持 .xlsx, .xls, .xlsm 格式
4. **编码**: 自动处理常见的编码问题
5. **日志**: 所有操作都会记录日志，便于调试

## 常见问题

### Q: 如何处理大文件？
A: 使用`nrows`参数分批读取，或使用`usecols`只读取需要的列。

### Q: 如何处理合并单元格？
A: 使用`ExcelDataProcessor.handle_merged_cells()`方法。

### Q: 如何自定义数据类型？
A: 使用`dtype`参数或`ExcelDataProcessor.convert_data_types()`方法。

### Q: 如何处理中文文件名？
A: 模块完全支持中文文件名和路径。

## 更新日志

- v1.0.0: 初始版本，包含基本读写功能
- 支持中文注释和文档
- 完整的错误处理和数据验证
- 高级数据处理功能
