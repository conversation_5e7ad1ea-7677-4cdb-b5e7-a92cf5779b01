from core.context import context_manager
from core.setting import SETTINGS
qmt_trader = context_manager.module_obj("qmt_trader")
qmt_trader.connect(SETTINGS)

# print(qmt_trader.buy("002138.SZ", order_volume=100, price=31.69, strategy_name="test"))
# print(qmt_trader.buy("002138.SZ", order_volume=100, price=31.69))
print(qmt_trader.query_stock_orders()[0].stock_code)
print(qmt_trader.query_stock_orders()[0].order_time)