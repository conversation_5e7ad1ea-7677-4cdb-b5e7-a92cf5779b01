"""
Excel模块测试脚本

测试excel.py模块的基本功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import excel
    print("✓ Excel模块导入成功")
except ImportError as e:
    print(f"✗ Excel模块导入失败: {e}")
    sys.exit(1)

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '股票代码': ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH'],
        '股票名称': ['平安银行', '万科A', '浦发银行', '招商银行'],
        '当前价格': [12.50, 18.30, 9.80, 45.20],
        '成交量': [1500000, 2300000, 1800000, 980000],
        '涨跌幅': [0.02, -0.015, 0.008, 0.035],
        '日期': pd.date_range('2025-08-16', periods=4)
    })
    
    print("创建的测试数据:")
    print(test_data)
    
    # 测试写入Excel
    test_file = "test_output.xlsx"
    try:
        excel.write_excel(test_data, test_file, sheet_name='股票数据', index=False)
        print(f"✓ 成功写入Excel文件: {test_file}")
    except Exception as e:
        print(f"✗ 写入Excel失败: {e}")
        return False
    
    # 测试读取Excel
    try:
        df_read = excel.read_excel(test_file, sheet='股票数据')
        print("✓ 成功读取Excel文件")
        print("读取的数据形状:", df_read.shape)
        print("读取的列名:", list(df_read.columns))
    except Exception as e:
        print(f"✗ 读取Excel失败: {e}")
        return False
    
    # 测试文件信息
    try:
        file_info = excel.get_excel_info(test_file)
        print("✓ 成功获取文件信息")
        print(f"  文件大小: {file_info.get('size_mb', 'N/A')} MB")
        print(f"  工作表数量: {file_info.get('sheet_count', 'N/A')}")
        print(f"  工作表名称: {file_info.get('sheet_names', 'N/A')}")
    except Exception as e:
        print(f"✗ 获取文件信息失败: {e}")
        return False
    
    # 测试数据清理
    try:
        # 创建有问题的数据
        messy_data = test_data.copy()
        messy_data.loc[1, '股票名称'] = '  万科A  '  # 添加空格
        messy_data.loc[2, '当前价格'] = None  # 添加空值
        
        cleaned_data = excel.clean_excel_data(
            messy_data, 
            strip_strings=True, 
            remove_empty_rows=False
        )
        print("✓ 成功清理数据")
        print("清理前后数据形状:", messy_data.shape, "->", cleaned_data.shape)
    except Exception as e:
        print(f"✗ 数据清理失败: {e}")
        return False
    
    # 清理测试文件
    try:
        if Path(test_file).exists():
            Path(test_file).unlink()
            print(f"✓ 清理测试文件: {test_file}")
    except Exception as e:
        print(f"⚠ 清理测试文件失败: {e}")
    
    return True

def test_multiple_sheets():
    """测试多工作表功能"""
    print("\n=== 测试多工作表功能 ===")
    
    # 创建多个数据集
    data1 = pd.DataFrame({
        '产品': ['产品A', '产品B', '产品C'],
        '销量': [100, 150, 200],
        '价格': [10.5, 15.8, 20.3]
    })
    
    data2 = pd.DataFrame({
        '月份': ['1月', '2月', '3月'],
        '收入': [50000, 60000, 75000],
        '支出': [30000, 35000, 40000]
    })
    
    data_dict = {
        '产品数据': data1,
        '财务数据': data2,
        '汇总统计': data1.describe()
    }
    
    test_file = "test_multiple_sheets.xlsx"
    
    try:
        # 写入多个工作表
        excel.write_multiple_excel_sheets(data_dict, test_file)
        print(f"✓ 成功写入多工作表文件: {test_file}")
        
        # 读取多个工作表
        all_sheets = excel.read_multiple_excel_sheets(test_file)
        print(f"✓ 成功读取 {len(all_sheets)} 个工作表")
        
        for sheet_name, df in all_sheets.items():
            print(f"  工作表 '{sheet_name}': {df.shape}")
            
    except Exception as e:
        print(f"✗ 多工作表操作失败: {e}")
        return False
    finally:
        # 清理测试文件
        try:
            if Path(test_file).exists():
                Path(test_file).unlink()
                print(f"✓ 清理测试文件: {test_file}")
        except Exception as e:
            print(f"⚠ 清理测试文件失败: {e}")
    
    return True

def main():
    """主测试函数"""
    print("开始测试Excel工具模块...")
    
    # 检查pandas是否可用
    try:
        print(f"Pandas版本: {pd.__version__}")
        print(f"Numpy版本: {np.__version__}")
    except Exception as e:
        print(f"依赖库检查失败: {e}")
        return
    
    # 运行测试
    tests = [
        ("基本功能测试", test_basic_functionality),
        ("多工作表测试", test_multiple_sheets),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"运行测试: {test_name}")
        print('='*60)
        
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*60)
    
    if passed == total:
        print("🎉 所有测试通过！Excel模块工作正常。")
    else:
        print("⚠ 部分测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()
