from core.context import context_manager
from core.setting import SETTINGS
from core.utility import write_csv
import time
import os
import requests
qmt_data = context_manager.module_obj("qmt_data")


code_lst = qmt_data.get_vt_symbols()
print(len(code_lst))

# def on_data(data):
#     print(data)
# qmt_data.download_history_data2(code_lst, period="1d", start_time="20250801", callback=on_data)

def save_full_tick():
    full_tick = qmt_data.get_full_tick(code_lst)
    info = []
    day = None
    for k, v in full_tick.items():
        if not day:
            day = v['timetag'].split()[0]
        info.append({
            "xt_symbol": k,
            "last_price": v["lastPrice"],
            "open": v["open"],
            "high": v["high"],
            "low": v["low"],
            "pre_close": v["lastClose"],
            "volume": v["volume"],
            "amount": v["amount"],
        })
    save_dir = os.path.join(os.getcwd(),"data", day)
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    write_csv(os.path.join(save_dir, "full_tick.csv"), info)

def save_trader_day():
    url = "https://www.szse.cn/api/report/exchange/onepersistenthour/monthList?random=0.7029446923318416"
    data = requests.get(url).json()
    # print(data)
    for x in data["data"]:
        if x["jybz"] == "1":
            print(x)
# while True:
#     time.sleep(1)
def save_stock_pool():
    info = []
    code_lst = qmt_data.get_vt_symbols()
    for vt_symbol in code_lst:
        data =qmt_data.get_stock_contract(vt_symbol)
        float_volume = data["FloatVolume"]
        fotal_volume = data["TotalVolume"]
        limit_up_price = data["UpStopPrice"]
        info.append({
            "股票代码": vt_symbol,
            "流通股本": float_volume,
            "总股本": fotal_volume,
            "涨停价": limit_up_price,
        })
    write_csv(os.path.join("data", "股票池.csv"), info)
# save_trader_day()
save_stock_pool()