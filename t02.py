import panda as pd

from xtquant import xtdata

# 写入excle
def get_stock_detail(self, stock_list):
    self.download_sector_data()
    stock_info = {}
    for x in stock_list:
        d = xtdata.get_instrument_detail(x)
        stock_info[x] = {
            "name": d['InstrumentName'],
            "float_volume": d['FloatVolume'],
            "float_amount": round(d['FloatVolume'] * d['PreClose'], 2) / 10 ** 8,
            "total_volume": d['TotalVolume'],
            "ratio": self.get_stock_ratio(x),
        }

    return stock_info
def get_vt_symbols(self):
    xt_symbols: list[str] = []
    markets: list = [
        "沪深A股",
        # "沪深转债",
        # "沪深ETF",
        # "沪深指数",
        "京市A股"
    ]

    for i in markets:
        names: list = xtdata.get_stock_list_in_sector(i)
        xt_symbols.extend(names)
    return xt_symbols


