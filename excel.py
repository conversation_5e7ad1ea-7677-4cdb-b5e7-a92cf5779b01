"""
Comprehensive Excel utility module for pandas-based Excel operations.

This module provides a complete set of tools for reading, writing, and manipulating
Excel files with robust error handling, data validation, and advanced features.

Author: Augment Agent
Date: 2025-08-16
"""

import shutil
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime
import warnings

import pandas as pd
import numpy as np

# Configure logging
logger = logging.getLogger(__name__)

# Suppress openpyxl warnings for better user experience
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')


class ExcelError(Exception):
    """Custom exception for Excel operations."""
    pass


class ExcelReader:
    """
    Excel file reading operations with advanced features.
    
    Provides methods for reading Excel files with configurable options,
    error handling, and data validation.
    """
    
    def __init__(self, file_path: Union[str, Path]):
        """
        Initialize ExcelReader with file path.
        
        Args:
            file_path: Path to the Excel file
            
        Raises:
            ExcelError: If file doesn't exist or is not accessible
        """
        self.file_path = Path(file_path)
        self._validate_file()
        
    def _validate_file(self) -> None:
        """Validate that the file exists and is accessible."""
        if not self.file_path.exists():
            raise ExcelError(f"File not found: {self.file_path}")
        
        if not self.file_path.is_file():
            raise ExcelError(f"Path is not a file: {self.file_path}")
            
        if self.file_path.suffix.lower() not in ['.xlsx', '.xls', '.xlsm']:
            raise ExcelError(f"Unsupported file format: {self.file_path.suffix}")
    
    def get_sheet_names(self) -> List[str]:
        """
        Get list of sheet names in the Excel file.
        
        Returns:
            List of sheet names
            
        Raises:
            ExcelError: If file cannot be read
        """
        try:
            with pd.ExcelFile(self.file_path) as excel_file:
                return excel_file.sheet_names
        except Exception as e:
            raise ExcelError(f"Failed to read sheet names: {str(e)}")
    
    def read_sheet(
        self,
        sheet: Union[str, int] = 0,
        header: Union[int, List[int], None] = 0,
        index_col: Union[int, str, List[Union[int, str]], None] = None,
        usecols: Union[str, List[Union[int, str]], None] = None,
        skiprows: Union[int, List[int], None] = None,
        nrows: Optional[int] = None,
        na_values: Union[str, List[str], Dict[str, List[str]], None] = None,
        dtype: Union[str, Dict[str, str], None] = None,
        converters: Optional[Dict[str, Callable]] = None,
        parse_dates: Union[bool, List[Union[int, str]], Dict[str, List[Union[int, str]]]] = False,
        date_parser: Optional[Callable] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        Read a specific sheet from the Excel file with comprehensive options.
        
        Args:
            sheet: Sheet name (str) or index (int) to read
            header: Row(s) to use as column names
            index_col: Column(s) to use as row labels
            usecols: Columns to read (by name or index)
            skiprows: Rows to skip at the beginning
            nrows: Number of rows to read
            na_values: Values to recognize as NaN
            dtype: Data type for columns
            converters: Dict of functions for converting values
            parse_dates: Parse dates
            date_parser: Function to parse dates
            **kwargs: Additional arguments for pd.read_excel
            
        Returns:
            DataFrame containing the sheet data
            
        Raises:
            ExcelError: If sheet cannot be read
        """
        try:
            logger.info(f"Reading sheet '{sheet}' from {self.file_path}")
            
            df = pd.read_excel(
                self.file_path,
                sheet_name=sheet,
                header=header,
                index_col=index_col,
                usecols=usecols,
                skiprows=skiprows,
                nrows=nrows,
                na_values=na_values,
                dtype=dtype,
                converters=converters,
                parse_dates=parse_dates,
                date_parser=date_parser,
                **kwargs
            )
            
            logger.info(f"Successfully read sheet with shape {df.shape}")
            return df
            
        except Exception as e:
            raise ExcelError(f"Failed to read sheet '{sheet}': {str(e)}")
    
    def read_range(
        self,
        sheet: Union[str, int] = 0,
        start_row: int = 0,
        end_row: Optional[int] = None,
        start_col: Union[int, str] = 0,
        end_col: Union[int, str, None] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        Read a specific range from an Excel sheet.
        
        Args:
            sheet: Sheet name or index
            start_row: Starting row (0-indexed)
            end_row: Ending row (0-indexed, None for all)
            start_col: Starting column (0-indexed or column name)
            end_col: Ending column (0-indexed, column name, or None for all)
            **kwargs: Additional arguments for read_sheet
            
        Returns:
            DataFrame containing the range data
        """
        # Calculate skiprows and nrows
        skiprows = start_row if start_row > 0 else None
        nrows = (end_row - start_row + 1) if end_row is not None else None
        
        # Handle column selection
        usecols = None
        if isinstance(start_col, int) and isinstance(end_col, int):
            usecols = list(range(start_col, end_col + 1))
        elif isinstance(start_col, str) and isinstance(end_col, str):
            usecols = f"{start_col}:{end_col}"
        elif start_col is not None:
            usecols = start_col
            
        return self.read_sheet(
            sheet=sheet,
            skiprows=skiprows,
            nrows=nrows,
            usecols=usecols,
            **kwargs
        )
    
    def read_multiple_sheets(
        self,
        sheets: Optional[List[Union[str, int]]] = None,
        **kwargs
    ) -> Dict[str, pd.DataFrame]:
        """
        Read multiple sheets from the Excel file.
        
        Args:
            sheets: List of sheet names/indices to read (None for all)
            **kwargs: Additional arguments for read_sheet
            
        Returns:
            Dictionary mapping sheet names to DataFrames
        """
        if sheets is None:
            sheets = self.get_sheet_names()
        
        result = {}
        for sheet in sheets:
            try:
                result[str(sheet)] = self.read_sheet(sheet, **kwargs)
            except Exception as e:
                logger.warning(f"Failed to read sheet '{sheet}': {str(e)}")
                
        return result


class ExcelWriter:
    """
    Excel file writing operations with advanced formatting and features.

    Provides methods for writing DataFrames to Excel files with customizable
    formatting, multiple sheet support, and backup functionality.
    """

    def __init__(self, file_path: Union[str, Path], create_backup: bool = True):
        """
        Initialize ExcelWriter with file path.

        Args:
            file_path: Path to the Excel file
            create_backup: Whether to create backup before overwriting
        """
        self.file_path = Path(file_path)
        self.create_backup = create_backup
        self._ensure_directory()

    def _ensure_directory(self) -> None:
        """Ensure the directory exists for the file path."""
        self.file_path.parent.mkdir(parents=True, exist_ok=True)

    def _create_backup(self) -> Optional[Path]:
        """
        Create a backup of the existing file.

        Returns:
            Path to backup file if created, None otherwise
        """
        if not self.file_path.exists() or not self.create_backup:
            return None

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.file_path.with_name(
            f"{self.file_path.stem}_backup_{timestamp}{self.file_path.suffix}"
        )

        try:
            shutil.copy2(self.file_path, backup_path)
            logger.info(f"Created backup: {backup_path}")
            return backup_path
        except Exception as e:
            logger.warning(f"Failed to create backup: {str(e)}")
            return None

    def write_dataframe(
        self,
        df: pd.DataFrame,
        sheet_name: str = 'Sheet1',
        index: bool = True,
        header: bool = True,
        startrow: int = 0,
        startcol: int = 0,
        engine: str = 'openpyxl',
        mode: str = 'w',
        if_sheet_exists: str = 'error',
        **kwargs
    ) -> None:
        """
        Write a DataFrame to an Excel sheet.

        Args:
            df: DataFrame to write
            sheet_name: Name of the sheet
            index: Whether to write row names (index)
            header: Whether to write column names
            startrow: Upper left cell row to dump data frame
            startcol: Upper left cell column to dump data frame
            engine: Write engine to use
            mode: File mode ('w' for write, 'a' for append)
            if_sheet_exists: How to behave if sheet exists ('error', 'new', 'replace', 'overlay')
            **kwargs: Additional arguments for to_excel

        Raises:
            ExcelError: If writing fails
        """
        try:
            # Create backup if needed
            self._create_backup()

            logger.info(f"Writing DataFrame to sheet '{sheet_name}' in {self.file_path}")

            with pd.ExcelWriter(
                self.file_path,
                engine=engine,
                mode=mode,
                if_sheet_exists=if_sheet_exists
            ) as writer:
                df.to_excel(
                    writer,
                    sheet_name=sheet_name,
                    index=index,
                    header=header,
                    startrow=startrow,
                    startcol=startcol,
                    **kwargs
                )

            logger.info(f"Successfully wrote DataFrame with shape {df.shape}")

        except Exception as e:
            error_msg = f"Failed to write DataFrame: {str(e)}"
            logger.error(error_msg)
            raise ExcelError(error_msg)

    def write_multiple_sheets(
        self,
        data_dict: Dict[str, pd.DataFrame],
        index: bool = True,
        header: bool = True,
        engine: str = 'openpyxl',
        **kwargs
    ) -> None:
        """
        Write multiple DataFrames to different sheets in one Excel file.

        Args:
            data_dict: Dictionary mapping sheet names to DataFrames
            index: Whether to write row names (index)
            header: Whether to write column names
            engine: Write engine to use
            **kwargs: Additional arguments for to_excel

        Raises:
            ExcelError: If writing fails
        """
        try:
            # Create backup if needed
            backup_path = self._create_backup()

            logger.info(f"Writing {len(data_dict)} sheets to {self.file_path}")

            with pd.ExcelWriter(self.file_path, engine=engine) as writer:
                for sheet_name, df in data_dict.items():
                    df.to_excel(
                        writer,
                        sheet_name=sheet_name,
                        index=index,
                        header=header,
                        **kwargs
                    )
                    logger.info(f"Wrote sheet '{sheet_name}' with shape {df.shape}")

            logger.info("Successfully wrote all sheets")

        except Exception as e:
            error_msg = f"Failed to write multiple sheets: {str(e)}"
            logger.error(error_msg)
            raise ExcelError(error_msg)

    def append_to_sheet(
        self,
        df: pd.DataFrame,
        sheet_name: str = 'Sheet1',
        header: bool = False,
        **kwargs
    ) -> None:
        """
        Append DataFrame to an existing sheet.

        Args:
            df: DataFrame to append
            sheet_name: Name of the sheet
            header: Whether to write column names
            **kwargs: Additional arguments for to_excel

        Raises:
            ExcelError: If appending fails
        """
        try:
            if not self.file_path.exists():
                # If file doesn't exist, write normally
                self.write_dataframe(df, sheet_name=sheet_name, header=True, **kwargs)
                return

            # Read existing data to find the next row
            reader = ExcelReader(self.file_path)
            try:
                existing_df = reader.read_sheet(sheet_name)
                startrow = len(existing_df) + 1  # +1 for header
            except ExcelError:
                # Sheet doesn't exist, start from beginning
                startrow = 0
                header = True

            # Append the data
            self.write_dataframe(
                df,
                sheet_name=sheet_name,
                mode='a',
                if_sheet_exists='overlay',
                startrow=startrow,
                header=header,
                **kwargs
            )

        except Exception as e:
            error_msg = f"Failed to append to sheet: {str(e)}"
            logger.error(error_msg)
            raise ExcelError(error_msg)


class ExcelDataProcessor:
    """
    Data manipulation and preprocessing utilities for Excel data.

    Provides methods for cleaning, transforming, and validating data
    commonly found in Excel files.
    """

    @staticmethod
    def clean_dataframe(
        df: pd.DataFrame,
        remove_empty_rows: bool = True,
        remove_empty_cols: bool = True,
        strip_strings: bool = True,
        standardize_headers: bool = True
    ) -> pd.DataFrame:
        """
        Clean a DataFrame with common preprocessing steps.

        Args:
            df: DataFrame to clean
            remove_empty_rows: Remove rows that are entirely empty
            remove_empty_cols: Remove columns that are entirely empty
            strip_strings: Strip whitespace from string columns
            standardize_headers: Standardize column names

        Returns:
            Cleaned DataFrame
        """
        df_clean = df.copy()

        # Remove empty rows and columns
        if remove_empty_rows:
            df_clean = df_clean.dropna(how='all')

        if remove_empty_cols:
            df_clean = df_clean.dropna(axis=1, how='all')

        # Strip whitespace from string columns
        if strip_strings:
            string_cols = df_clean.select_dtypes(include=['object']).columns
            for col in string_cols:
                df_clean[col] = df_clean[col].astype(str).str.strip()
                # Convert back to NaN if it was originally NaN
                df_clean[col] = df_clean[col].replace('nan', np.nan)

        # Standardize headers
        if standardize_headers:
            df_clean.columns = [
                str(col).strip().replace(' ', '_').lower()
                for col in df_clean.columns
            ]

        logger.info(f"Cleaned DataFrame: {df.shape} -> {df_clean.shape}")
        return df_clean

    @staticmethod
    def convert_data_types(
        df: pd.DataFrame,
        type_mapping: Dict[str, str],
        errors: str = 'coerce'
    ) -> pd.DataFrame:
        """
        Convert DataFrame columns to specified data types.

        Args:
            df: DataFrame to convert
            type_mapping: Dictionary mapping column names to data types
            errors: How to handle conversion errors ('raise', 'coerce', 'ignore')

        Returns:
            DataFrame with converted types
        """
        df_converted = df.copy()

        for col, dtype in type_mapping.items():
            if col in df_converted.columns:
                try:
                    if dtype.lower() in ['datetime', 'date']:
                        df_converted[col] = pd.to_datetime(df_converted[col], errors=errors)
                    elif dtype.lower() in ['int', 'integer']:
                        df_converted[col] = pd.to_numeric(df_converted[col], errors=errors).astype('Int64')
                    elif dtype.lower() in ['float', 'numeric']:
                        df_converted[col] = pd.to_numeric(df_converted[col], errors=errors)
                    elif dtype.lower() in ['str', 'string']:
                        df_converted[col] = df_converted[col].astype(str)
                    elif dtype.lower() in ['bool', 'boolean']:
                        df_converted[col] = df_converted[col].astype(bool)
                    else:
                        df_converted[col] = df_converted[col].astype(dtype)

                    logger.info(f"Converted column '{col}' to {dtype}")

                except Exception as e:
                    logger.warning(f"Failed to convert column '{col}' to {dtype}: {str(e)}")

        return df_converted

    @staticmethod
    def handle_merged_cells(df: pd.DataFrame, fill_method: str = 'ffill') -> pd.DataFrame:
        """
        Handle merged cells by filling missing values.

        Args:
            df: DataFrame with potential merged cell issues
            fill_method: Method to fill missing values ('ffill', 'bfill', 'interpolate')

        Returns:
            DataFrame with filled values
        """
        df_filled = df.copy()

        if fill_method == 'ffill':
            df_filled = df_filled.fillna(method='ffill')
        elif fill_method == 'bfill':
            df_filled = df_filled.fillna(method='bfill')
        elif fill_method == 'interpolate':
            numeric_cols = df_filled.select_dtypes(include=[np.number]).columns
            df_filled[numeric_cols] = df_filled[numeric_cols].interpolate()

        logger.info(f"Handled merged cells using {fill_method} method")
        return df_filled

    @staticmethod
    def detect_and_fix_encoding_issues(df: pd.DataFrame) -> pd.DataFrame:
        """
        Detect and fix common encoding issues in string columns.

        Args:
            df: DataFrame to fix

        Returns:
            DataFrame with fixed encoding
        """
        df_fixed = df.copy()
        string_cols = df_fixed.select_dtypes(include=['object']).columns

        for col in string_cols:
            # Fix common encoding issues
            df_fixed[col] = df_fixed[col].astype(str).replace({
                'â€™': "'",
                'â€œ': '"',
                'â€': '"',
                'â€"': '—',
                'â€"': '–',
                'Â': '',
            }, regex=True)

        return df_fixed


class ExcelUtilities:
    """
    Additional utility functions for Excel operations.

    Provides file validation, progress tracking, and other helper functions.
    """

    @staticmethod
    def validate_file_path(file_path: Union[str, Path]) -> Path:
        """
        Validate and normalize file path.

        Args:
            file_path: Path to validate

        Returns:
            Validated Path object

        Raises:
            ExcelError: If path is invalid
        """
        path = Path(file_path)

        if path.suffix.lower() not in ['.xlsx', '.xls', '.xlsm']:
            raise ExcelError(f"Invalid Excel file extension: {path.suffix}")

        return path

    @staticmethod
    def get_file_info(file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get information about an Excel file.

        Args:
            file_path: Path to the Excel file

        Returns:
            Dictionary with file information
        """
        path = Path(file_path)

        if not path.exists():
            return {"exists": False}

        try:
            reader = ExcelReader(path)
            sheet_names = reader.get_sheet_names()

            # Get basic file stats
            stat = path.stat()

            return {
                "exists": True,
                "size_bytes": stat.st_size,
                "size_mb": round(stat.st_size / (1024 * 1024), 2),
                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                "sheet_count": len(sheet_names),
                "sheet_names": sheet_names,
                "file_extension": path.suffix.lower()
            }
        except Exception as e:
            return {
                "exists": True,
                "error": str(e)
            }

    @staticmethod
    def create_progress_callback(total_operations: int) -> Callable:
        """
        Create a progress callback function for large operations.

        Args:
            total_operations: Total number of operations

        Returns:
            Progress callback function
        """
        def progress_callback(current: int, message: str = ""):
            percentage = (current / total_operations) * 100
            logger.info(f"Progress: {percentage:.1f}% ({current}/{total_operations}) {message}")

        return progress_callback

    @staticmethod
    def batch_process_files(
        file_paths: List[Union[str, Path]],
        operation: Callable,
        progress_callback: Optional[Callable] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process multiple Excel files in batch.

        Args:
            file_paths: List of file paths to process
            operation: Function to apply to each file
            progress_callback: Optional progress callback
            **kwargs: Additional arguments for operation

        Returns:
            Dictionary with results for each file
        """
        results = {}
        total_files = len(file_paths)

        if progress_callback is None:
            progress_callback = ExcelUtilities.create_progress_callback(total_files)

        for i, file_path in enumerate(file_paths):
            try:
                progress_callback(i, f"Processing {file_path}")
                results[str(file_path)] = operation(file_path, **kwargs)
            except Exception as e:
                results[str(file_path)] = {"error": str(e)}
                logger.error(f"Failed to process {file_path}: {str(e)}")

        progress_callback(total_files, "Batch processing complete")
        return results


# Main interface functions for easy usage
def read_excel(
    file_path: Union[str, Path],
    sheet: Union[str, int] = 0,
    **kwargs
) -> pd.DataFrame:
    """
    Simple interface to read an Excel file.

    Args:
        file_path: Path to the Excel file
        sheet: Sheet name or index to read
        **kwargs: Additional arguments for ExcelReader.read_sheet

    Returns:
        DataFrame containing the sheet data

    Example:
        >>> df = read_excel('data.xlsx', sheet='Sheet1')
        >>> df = read_excel('data.xlsx', sheet=0, header=1, usecols='A:D')
    """
    reader = ExcelReader(file_path)
    return reader.read_sheet(sheet, **kwargs)


def write_excel(
    df: pd.DataFrame,
    file_path: Union[str, Path],
    sheet_name: str = 'Sheet1',
    **kwargs
) -> None:
    """
    Simple interface to write a DataFrame to Excel.

    Args:
        df: DataFrame to write
        file_path: Path to the Excel file
        sheet_name: Name of the sheet
        **kwargs: Additional arguments for ExcelWriter.write_dataframe

    Example:
        >>> write_excel(df, 'output.xlsx', sheet_name='Data')
        >>> write_excel(df, 'output.xlsx', index=False, header=True)
    """
    writer = ExcelWriter(file_path)
    writer.write_dataframe(df, sheet_name, **kwargs)


def read_multiple_excel_sheets(
    file_path: Union[str, Path],
    sheets: Optional[List[Union[str, int]]] = None,
    **kwargs
) -> Dict[str, pd.DataFrame]:
    """
    Read multiple sheets from an Excel file.

    Args:
        file_path: Path to the Excel file
        sheets: List of sheet names/indices (None for all)
        **kwargs: Additional arguments for reading

    Returns:
        Dictionary mapping sheet names to DataFrames

    Example:
        >>> data = read_multiple_excel_sheets('data.xlsx')
        >>> data = read_multiple_excel_sheets('data.xlsx', sheets=['Sheet1', 'Sheet2'])
    """
    reader = ExcelReader(file_path)
    return reader.read_multiple_sheets(sheets, **kwargs)


def write_multiple_excel_sheets(
    data_dict: Dict[str, pd.DataFrame],
    file_path: Union[str, Path],
    **kwargs
) -> None:
    """
    Write multiple DataFrames to different sheets in one Excel file.

    Args:
        data_dict: Dictionary mapping sheet names to DataFrames
        file_path: Path to the Excel file
        **kwargs: Additional arguments for writing

    Example:
        >>> data = {'Sheet1': df1, 'Sheet2': df2}
        >>> write_multiple_excel_sheets(data, 'output.xlsx')
    """
    writer = ExcelWriter(file_path)
    writer.write_multiple_sheets(data_dict, **kwargs)


def clean_excel_data(
    df: pd.DataFrame,
    **kwargs
) -> pd.DataFrame:
    """
    Clean Excel data with common preprocessing steps.

    Args:
        df: DataFrame to clean
        **kwargs: Additional arguments for ExcelDataProcessor.clean_dataframe

    Returns:
        Cleaned DataFrame

    Example:
        >>> df_clean = clean_excel_data(df, remove_empty_rows=True, strip_strings=True)
    """
    return ExcelDataProcessor.clean_dataframe(df, **kwargs)


def get_excel_info(file_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Get information about an Excel file.

    Args:
        file_path: Path to the Excel file

    Returns:
        Dictionary with file information

    Example:
        >>> info = get_excel_info('data.xlsx')
        >>> print(f"File has {info['sheet_count']} sheets")
    """
    return ExcelUtilities.get_file_info(file_path)


# Example usage and testing functions
def _create_sample_data() -> pd.DataFrame:
    """Create sample data for testing."""
    return pd.DataFrame({
        '股票代码': ['000001.SZ', '000002.SZ', '600000.SH'],
        '股票名称': ['平安银行', '万科A', '浦发银行'],
        '价格': [10.5, 25.3, 8.9],
        '成交量': [1000000, 2000000, 1500000],
        '日期': pd.date_range('2025-01-01', periods=3)
    })


def example_usage():
    """
    Demonstrate the usage of the Excel utility module.

    This function shows various ways to use the Excel utilities
    for reading, writing, and processing Excel files.
    """
    print("Excel Utility Module - Example Usage")
    print("=" * 50)

    # Create sample data
    sample_df = _create_sample_data()
    print("Sample data created:")
    print(sample_df)
    print()

    # Example 1: Simple write and read
    print("1. Simple write and read operations:")
    file_path = "example_output.xlsx"

    # Write data
    write_excel(sample_df, file_path, sheet_name='股票数据', index=False)
    print(f"Data written to {file_path}")

    # Read data back
    df_read = read_excel(file_path, sheet='股票数据')
    print("Data read back:")
    print(df_read.head())
    print()

    # Example 2: Multiple sheets
    print("2. Multiple sheets operations:")
    data_dict = {
        '股票数据': sample_df,
        '统计信息': sample_df.describe(),
        '汇总': sample_df.groupby('股票代码').sum()
    }

    multi_file = "multi_sheet_example.xlsx"
    write_multiple_excel_sheets(data_dict, multi_file)
    print(f"Multiple sheets written to {multi_file}")

    # Read multiple sheets
    all_sheets = read_multiple_excel_sheets(multi_file)
    print(f"Read {len(all_sheets)} sheets: {list(all_sheets.keys())}")
    print()

    # Example 3: Data cleaning
    print("3. Data cleaning operations:")
    # Create messy data
    messy_df = sample_df.copy()
    messy_df.loc[1, '股票名称'] = '  万科A  '  # Add whitespace
    messy_df.loc[2, '价格'] = None  # Add missing value

    print("Messy data:")
    print(messy_df)

    # Clean the data
    clean_df = clean_excel_data(messy_df, strip_strings=True, remove_empty_rows=False)
    print("Cleaned data:")
    print(clean_df)
    print()

    # Example 4: File information
    print("4. File information:")
    info = get_excel_info(file_path)
    print(f"File info for {file_path}:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    print()

    # Example 5: Advanced reading with options
    print("5. Advanced reading options:")
    # Read specific columns
    df_subset = read_excel(file_path, sheet='股票数据', usecols=['股票代码', '价格'])
    print("Reading specific columns:")
    print(df_subset)
    print()

    # Read with data type specification
    dtype_mapping = {'股票代码': 'str', '价格': 'float'}
    df_typed = read_excel(file_path, sheet='股票数据', dtype=dtype_mapping)
    print("Data types after reading:")
    print(df_typed.dtypes)
    print()

    print("Example usage completed successfully!")


if __name__ == "__main__":
    # Run example usage when script is executed directly
    example_usage()
