"""
Global setting of the trading platform.
"""
from logging import <PERSON><PERSON><PERSON><PERSON>
from tzlocal import get_localzone_name

from .utility import load_json


SETTINGS: dict = {
    "log.active": True,
    "log.level": CRITICAL,
    "log.console": True,
    "log.file": True,

    "email.server": "smtp.qq.com",
    "email.port": 465,
    "email.username": "",
    "email.password": "",
    "email.sender": "",
    "email.receiver": "",

    "datafeed.name": "",
    "datafeed.username": "",
    "datafeed.password": "",

    "qmt.path": rf"D:\国金证券QMT交易端\userdata_mini",
    "qmt.session_id": 123456,
    "qmt.access": "8882562152",
    "qmt.token": "",
    "日期昨天的": "20250815",

    "database.timezone": get_localzone_name(),
    "database.name": "taos",
    "database.database": "real_data_04",
    "database.host": "**************",
    "database.user": "root",
    "database.password": "taosdata",
    "database.port": 6041,

}


# Load global setting from json file.
# SETTINGS.update(load_json(SETTING_FILENAME))
