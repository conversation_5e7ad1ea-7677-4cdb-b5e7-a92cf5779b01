import pandas as pd
from core.utility import get_stock_ratio
from xtquant import xtdata
from excel import ExcelWriter

excel_writer = ExcelWriter("股票基本信息.xlsx")

def get_vt_symbols(markets):
    data_info = []
    for sec_name in markets:
        names: list = xtdata.get_stock_list_in_sector(sec_name)
        for x in names:
            d = xtdata.get_instrument_detail(x)
            ratio = get_stock_ratio(x)
            symbol, market  = x.split(".") 
            data_info.append({
                "股票代码": symbol,
                "股票名称": d['InstrumentName'],
                "流通股本": d['FloatVolume'],
                "总股本": d['TotalVolume'],
                "最大涨跌幅": ratio,
                "市场": market,
                "qmt股票代码": x,

            })
    return data_info


markets: list = [
    "沪深A股",
    # "沪深转债",
    # "沪深ETF",
    # "沪深指数",
    "京市A股"
]
all_info = {}
data_info = get_vt_symbols(markets)
all_info["股票"] = pd.DataFrame(data_info)
data_info = get_vt_symbols([
    "沪深转债",
])
all_info["可转债"] = pd.DataFrame(data_info)
data_info = get_vt_symbols([
    "沪深ETF",
])
all_info["ETF"] = pd.DataFrame(data_info)

data_info = get_vt_symbols([
    "沪深指数",
])
# df = df.drop(columns=['列名'])
all_info["指数"] = pd.DataFrame(data_info)

excel_writer.write_multiple_sheets(all_info, "股票基本信息.xlsx")

