"""
基于pandas的综合Excel工具模块。

本模块提供了一套完整的Excel文件读取、写入和操作工具，
具有强大的错误处理、数据验证和高级功能。

作者: Augment Agent
日期: 2025-08-16
"""

import shutil
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime
import warnings

import pandas as pd
import numpy as np

# 配置日志
logger = logging.getLogger(__name__)

# 抑制openpyxl警告以提供更好的用户体验
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')


class ExcelError(Exception):
    """Excel操作的自定义异常类。"""
    pass


class ExcelReader:
    """
    Excel文件读取操作类，具有高级功能。

    提供读取Excel文件的方法，支持可配置选项、
    错误处理和数据验证。
    """
    
    def __init__(self, file_path: Union[str, Path]):
        """
        使用文件路径初始化ExcelReader。

        参数:
            file_path: Excel文件的路径

        异常:
            ExcelError: 如果文件不存在或无法访问
        """
        self.file_path = Path(file_path)
        self._validate_file()

    def _validate_file(self) -> None:
        """验证文件是否存在且可访问。"""
        if not self.file_path.exists():
            raise ExcelError(f"文件未找到: {self.file_path}")

        if not self.file_path.is_file():
            raise ExcelError(f"路径不是文件: {self.file_path}")

        if self.file_path.suffix.lower() not in ['.xlsx', '.xls', '.xlsm']:
            raise ExcelError(f"不支持的文件格式: {self.file_path.suffix}")

    def get_sheet_names(self) -> List[str]:
        """
        获取Excel文件中的工作表名称列表。

        返回:
            工作表名称列表

        异常:
            ExcelError: 如果文件无法读取
        """
        try:
            with pd.ExcelFile(self.file_path) as excel_file:
                return excel_file.sheet_names
        except Exception as e:
            raise ExcelError(f"读取工作表名称失败: {str(e)}")
    
    def read_sheet(
        self,
        sheet: Union[str, int] = 0,
        header: Union[int, List[int], None] = 0,
        index_col: Union[int, str, List[Union[int, str]], None] = None,
        usecols: Union[str, List[Union[int, str]], None] = None,
        skiprows: Union[int, List[int], None] = None,
        nrows: Optional[int] = None,
        na_values: Union[str, List[str], Dict[str, List[str]], None] = None,
        dtype: Union[str, Dict[str, str], None] = None,
        converters: Optional[Dict[str, Callable]] = None,
        parse_dates: Union[bool, List[Union[int, str]], Dict[str, List[Union[int, str]]]] = False,
        date_parser: Optional[Callable] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        从Excel文件中读取指定工作表，支持全面的选项配置。

        参数:
            sheet: 要读取的工作表名称(str)或索引(int)
            header: 用作列名的行号
            index_col: 用作行标签的列
            usecols: 要读取的列(按名称或索引)
            skiprows: 开头要跳过的行数
            nrows: 要读取的行数
            na_values: 识别为NaN的值
            dtype: 列的数据类型
            converters: 值转换函数字典
            parse_dates: 解析日期
            date_parser: 日期解析函数
            **kwargs: pd.read_excel的其他参数

        返回:
            包含工作表数据的DataFrame

        异常:
            ExcelError: 如果工作表无法读取
        """
        try:
            logger.info(f"正在从 {self.file_path} 读取工作表 '{sheet}'")

            df = pd.read_excel(
                self.file_path,
                sheet_name=sheet,
                header=header,
                index_col=index_col,
                usecols=usecols,
                skiprows=skiprows,
                nrows=nrows,
                na_values=na_values,
                dtype=dtype,
                converters=converters,
                parse_dates=parse_dates,
                date_parser=date_parser,
                **kwargs
            )

            logger.info(f"成功读取工作表，形状为 {df.shape}")
            return df

        except Exception as e:
            raise ExcelError(f"读取工作表 '{sheet}' 失败: {str(e)}")
    
    def read_range(
        self,
        sheet: Union[str, int] = 0,
        start_row: int = 0,
        end_row: Optional[int] = None,
        start_col: Union[int, str] = 0,
        end_col: Union[int, str, None] = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        从Excel工作表中读取指定范围的数据。

        参数:
            sheet: 工作表名称或索引
            start_row: 起始行(从0开始)
            end_row: 结束行(从0开始，None表示全部)
            start_col: 起始列(从0开始的索引或列名)
            end_col: 结束列(从0开始的索引、列名或None表示全部)
            **kwargs: read_sheet的其他参数

        返回:
            包含范围数据的DataFrame
        """
        # 计算skiprows和nrows
        skiprows = start_row if start_row > 0 else None
        nrows = (end_row - start_row + 1) if end_row is not None else None

        # 处理列选择
        usecols = None
        if isinstance(start_col, int) and isinstance(end_col, int):
            usecols = list(range(start_col, end_col + 1))
        elif isinstance(start_col, str) and isinstance(end_col, str):
            usecols = f"{start_col}:{end_col}"
        elif start_col is not None:
            usecols = start_col

        return self.read_sheet(
            sheet=sheet,
            skiprows=skiprows,
            nrows=nrows,
            usecols=usecols,
            **kwargs
        )
    
    def read_multiple_sheets(
        self,
        sheets: Optional[List[Union[str, int]]] = None,
        **kwargs
    ) -> Dict[str, pd.DataFrame]:
        """
        从Excel文件中读取多个工作表。

        参数:
            sheets: 要读取的工作表名称/索引列表(None表示全部)
            **kwargs: read_sheet的其他参数

        返回:
            工作表名称到DataFrame的字典映射
        """
        if sheets is None:
            sheets = self.get_sheet_names()

        result = {}
        for sheet in sheets:
            try:
                result[str(sheet)] = self.read_sheet(sheet, **kwargs)
            except Exception as e:
                logger.warning(f"读取工作表 '{sheet}' 失败: {str(e)}")

        return result


class ExcelWriter:
    """
    Excel文件写入操作类，具有高级格式化和功能。

    提供将DataFrame写入Excel文件的方法，支持自定义格式化、
    多工作表支持和备份功能。
    """

    def __init__(self, file_path: Union[str, Path], create_backup: bool = True):
        """
        使用文件路径初始化ExcelWriter。

        参数:
            file_path: Excel文件的路径
            create_backup: 是否在覆盖前创建备份
        """
        self.file_path = Path(file_path)
        self.create_backup = create_backup
        self._ensure_directory()

    def _ensure_directory(self) -> None:
        """确保文件路径的目录存在。"""
        self.file_path.parent.mkdir(parents=True, exist_ok=True)

    def _create_backup(self) -> Optional[Path]:
        """
        创建现有文件的备份。

        返回:
            如果创建了备份文件则返回备份文件路径，否则返回None
        """
        if not self.file_path.exists() or not self.create_backup:
            return None

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.file_path.with_name(
            f"{self.file_path.stem}_backup_{timestamp}{self.file_path.suffix}"
        )

        try:
            shutil.copy2(self.file_path, backup_path)
            logger.info(f"已创建备份: {backup_path}")
            return backup_path
        except Exception as e:
            logger.warning(f"创建备份失败: {str(e)}")
            return None

    def write_dataframe(
        self,
        df: pd.DataFrame,
        sheet_name: str = 'Sheet1',
        index: bool = True,
        header: bool = True,
        startrow: int = 0,
        startcol: int = 0,
        engine: str = 'openpyxl',
        mode: str = 'w',
        if_sheet_exists: str = 'error',
        **kwargs
    ) -> None:
        """
        将DataFrame写入Excel工作表。

        参数:
            df: 要写入的DataFrame
            sheet_name: 工作表名称
            index: 是否写入行名(索引)
            header: 是否写入列名
            startrow: 数据框左上角单元格行号
            startcol: 数据框左上角单元格列号
            engine: 使用的写入引擎
            mode: 文件模式('w'表示写入，'a'表示追加)
            if_sheet_exists: 工作表存在时的行为('error', 'new', 'replace', 'overlay')
            **kwargs: to_excel的其他参数

        异常:
            ExcelError: 如果写入失败
        """
        try:
            # 如果需要则创建备份
            self._create_backup()

            logger.info(f"正在将DataFrame写入 {self.file_path} 的工作表 '{sheet_name}'")

            with pd.ExcelWriter(
                self.file_path,
                engine=engine,
                mode=mode,
                if_sheet_exists=if_sheet_exists
            ) as writer:
                df.to_excel(
                    writer,
                    sheet_name=sheet_name,
                    index=index,
                    header=header,
                    startrow=startrow,
                    startcol=startcol,
                    **kwargs
                )

            logger.info(f"成功写入DataFrame，形状为 {df.shape}")

        except Exception as e:
            error_msg = f"写入DataFrame失败: {str(e)}"
            logger.error(error_msg)
            raise ExcelError(error_msg)

    def write_multiple_sheets(
        self,
        data_dict: Dict[str, pd.DataFrame],
        index: bool = False,
        header: bool = True,
        engine: str = 'openpyxl',
        **kwargs
    ) -> None:
        """
        将多个DataFrame写入一个Excel文件的不同工作表。

        参数:
            data_dict: 工作表名称到DataFrame的字典映射
            index: 是否写入行名(索引)
            header: 是否写入列名
            engine: 使用的写入引擎
            **kwargs: to_excel的其他参数

        异常:
            ExcelError: 如果写入失败
        """
        try:
            # 如果需要则创建备份
            self._create_backup()

            logger.info(f"正在将 {len(data_dict)} 个工作表写入 {self.file_path}")

            with pd.ExcelWriter(self.file_path, engine=engine) as writer:
                for sheet_name, df in data_dict.items():
                    df.to_excel(
                        writer,
                        sheet_name=sheet_name,
                        index=index,
                        header=header,
                        **kwargs
                    )
                    logger.info(f"已写入工作表 '{sheet_name}'，形状为 {df.shape}")

            logger.info("成功写入所有工作表")

        except Exception as e:
            error_msg = f"写入多个工作表失败: {str(e)}"
            logger.error(error_msg)
            raise ExcelError(error_msg)

    def append_to_sheet(
        self,
        df: pd.DataFrame,
        sheet_name: str = 'Sheet1',
        header: bool = False,
        **kwargs
    ) -> None:
        """
        将DataFrame追加到现有工作表。

        参数:
            df: 要追加的DataFrame
            sheet_name: 工作表名称
            header: 是否写入列名
            **kwargs: to_excel的其他参数

        异常:
            ExcelError: 如果追加失败
        """
        try:
            if not self.file_path.exists():
                # 如果文件不存在，正常写入
                self.write_dataframe(df, sheet_name=sheet_name, header=True, **kwargs)
                return

            # 读取现有数据以找到下一行
            reader = ExcelReader(self.file_path)
            try:
                existing_df = reader.read_sheet(sheet_name)
                startrow = len(existing_df) + 1  # +1表示标题行
            except ExcelError:
                # 工作表不存在，从头开始
                startrow = 0
                header = True

            # 追加数据
            self.write_dataframe(
                df,
                sheet_name=sheet_name,
                mode='a',
                if_sheet_exists='overlay',
                startrow=startrow,
                header=header,
                **kwargs
            )

        except Exception as e:
            error_msg = f"追加到工作表失败: {str(e)}"
            logger.error(error_msg)
            raise ExcelError(error_msg)


class ExcelDataProcessor:
    """
    Excel数据操作和预处理工具类。

    提供清理、转换和验证Excel文件中常见数据的方法。
    """

    @staticmethod
    def clean_dataframe(
        df: pd.DataFrame,
        remove_empty_rows: bool = True,
        remove_empty_cols: bool = True,
        strip_strings: bool = True,
        standardize_headers: bool = True
    ) -> pd.DataFrame:
        """
        使用常见预处理步骤清理DataFrame。

        参数:
            df: 要清理的DataFrame
            remove_empty_rows: 删除完全为空的行
            remove_empty_cols: 删除完全为空的列
            strip_strings: 去除字符串列的空白字符
            standardize_headers: 标准化列名

        返回:
            清理后的DataFrame
        """
        df_clean = df.copy()

        # 删除空行和空列
        if remove_empty_rows:
            df_clean = df_clean.dropna(how='all')

        if remove_empty_cols:
            df_clean = df_clean.dropna(axis=1, how='all')

        # 去除字符串列的空白字符
        if strip_strings:
            string_cols = df_clean.select_dtypes(include=['object']).columns
            for col in string_cols:
                df_clean[col] = df_clean[col].astype(str).str.strip()
                # 如果原来是NaN则转换回NaN
                df_clean[col] = df_clean[col].replace('nan', np.nan)

        # 标准化标题
        if standardize_headers:
            df_clean.columns = [
                str(col).strip().replace(' ', '_').lower()
                for col in df_clean.columns
            ]

        logger.info(f"已清理DataFrame: {df.shape} -> {df_clean.shape}")
        return df_clean

    @staticmethod
    def convert_data_types(
        df: pd.DataFrame,
        type_mapping: Dict[str, str],
        errors: str = 'coerce'
    ) -> pd.DataFrame:
        """
        将DataFrame列转换为指定的数据类型。

        参数:
            df: 要转换的DataFrame
            type_mapping: 列名到数据类型的字典映射
            errors: 如何处理转换错误('raise', 'coerce', 'ignore')

        返回:
            转换类型后的DataFrame
        """
        df_converted = df.copy()

        for col, dtype in type_mapping.items():
            if col in df_converted.columns:
                try:
                    if dtype.lower() in ['datetime', 'date']:
                        df_converted[col] = pd.to_datetime(df_converted[col], errors=errors)
                    elif dtype.lower() in ['int', 'integer']:
                        df_converted[col] = pd.to_numeric(df_converted[col], errors=errors).astype('Int64')
                    elif dtype.lower() in ['float', 'numeric']:
                        df_converted[col] = pd.to_numeric(df_converted[col], errors=errors)
                    elif dtype.lower() in ['str', 'string']:
                        df_converted[col] = df_converted[col].astype(str)
                    elif dtype.lower() in ['bool', 'boolean']:
                        df_converted[col] = df_converted[col].astype(bool)
                    else:
                        df_converted[col] = df_converted[col].astype(dtype)

                    logger.info(f"已将列 '{col}' 转换为 {dtype}")

                except Exception as e:
                    logger.warning(f"将列 '{col}' 转换为 {dtype} 失败: {str(e)}")

        return df_converted

    @staticmethod
    def handle_merged_cells(df: pd.DataFrame, fill_method: str = 'ffill') -> pd.DataFrame:
        """
        通过填充缺失值来处理合并单元格。

        参数:
            df: 可能存在合并单元格问题的DataFrame
            fill_method: 填充缺失值的方法('ffill', 'bfill', 'interpolate')

        返回:
            填充值后的DataFrame
        """
        df_filled = df.copy()

        if fill_method == 'ffill':
            df_filled = df_filled.fillna(method='ffill')
        elif fill_method == 'bfill':
            df_filled = df_filled.fillna(method='bfill')
        elif fill_method == 'interpolate':
            numeric_cols = df_filled.select_dtypes(include=[np.number]).columns
            df_filled[numeric_cols] = df_filled[numeric_cols].interpolate()

        logger.info(f"使用 {fill_method} 方法处理了合并单元格")
        return df_filled

    @staticmethod
    def detect_and_fix_encoding_issues(df: pd.DataFrame) -> pd.DataFrame:
        """
        检测并修复字符串列中的常见编码问题。

        参数:
            df: 要修复的DataFrame

        返回:
            修复编码后的DataFrame
        """
        df_fixed = df.copy()
        string_cols = df_fixed.select_dtypes(include=['object']).columns

        for col in string_cols:
            # 修复常见编码问题
            df_fixed[col] = df_fixed[col].astype(str).replace({
                'â€™': "'",
                'â€œ': '"',
                'â€': '"',
                'â€"': '—',
                'â€"': '–',
                'Â': '',
            }, regex=True)

        return df_fixed


class ExcelUtilities:
    """
    Excel操作的附加工具函数类。

    提供文件验证、进度跟踪和其他辅助函数。
    """

    @staticmethod
    def validate_file_path(file_path: Union[str, Path]) -> Path:
        """
        验证并规范化文件路径。

        参数:
            file_path: 要验证的路径

        返回:
            验证后的Path对象

        异常:
            ExcelError: 如果路径无效
        """
        path = Path(file_path)

        if path.suffix.lower() not in ['.xlsx', '.xls', '.xlsm']:
            raise ExcelError(f"无效的Excel文件扩展名: {path.suffix}")

        return path

    @staticmethod
    def get_file_info(file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        获取Excel文件的信息。

        参数:
            file_path: Excel文件的路径

        返回:
            包含文件信息的字典
        """
        path = Path(file_path)

        if not path.exists():
            return {"exists": False}

        try:
            reader = ExcelReader(path)
            sheet_names = reader.get_sheet_names()

            # 获取基本文件统计信息
            stat = path.stat()

            return {
                "exists": True,
                "size_bytes": stat.st_size,
                "size_mb": round(stat.st_size / (1024 * 1024), 2),
                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                "sheet_count": len(sheet_names),
                "sheet_names": sheet_names,
                "file_extension": path.suffix.lower()
            }
        except Exception as e:
            return {
                "exists": True,
                "error": str(e)
            }

    @staticmethod
    def create_progress_callback(total_operations: int) -> Callable:
        """
        为大型操作创建进度回调函数。

        参数:
            total_operations: 总操作数

        返回:
            进度回调函数
        """
        def progress_callback(current: int, message: str = ""):
            percentage = (current / total_operations) * 100
            logger.info(f"进度: {percentage:.1f}% ({current}/{total_operations}) {message}")

        return progress_callback

    @staticmethod
    def batch_process_files(
        file_paths: List[Union[str, Path]],
        operation: Callable,
        progress_callback: Optional[Callable] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        批量处理多个Excel文件。

        参数:
            file_paths: 要处理的文件路径列表
            operation: 应用于每个文件的函数
            progress_callback: 可选的进度回调
            **kwargs: 操作的其他参数

        返回:
            每个文件结果的字典
        """
        results = {}
        total_files = len(file_paths)

        if progress_callback is None:
            progress_callback = ExcelUtilities.create_progress_callback(total_files)

        for i, file_path in enumerate(file_paths):
            try:
                progress_callback(i, f"正在处理 {file_path}")
                results[str(file_path)] = operation(file_path, **kwargs)
            except Exception as e:
                results[str(file_path)] = {"error": str(e)}
                logger.error(f"处理 {file_path} 失败: {str(e)}")

        progress_callback(total_files, "批量处理完成")
        return results


# 便于使用的主要接口函数
def read_excel(
    file_path: Union[str, Path],
    sheet: Union[str, int] = 0,
    **kwargs
) -> pd.DataFrame:
    """
    读取Excel文件的简单接口。

    参数:
        file_path: Excel文件的路径
        sheet: 要读取的工作表名称或索引
        **kwargs: ExcelReader.read_sheet的其他参数

    返回:
        包含工作表数据的DataFrame

    示例:
        >>> df = read_excel('data.xlsx', sheet='Sheet1')
        >>> df = read_excel('data.xlsx', sheet=0, header=1, usecols='A:D')
    """
    reader = ExcelReader(file_path)
    return reader.read_sheet(sheet, **kwargs)


def write_excel(
    df: pd.DataFrame,
    file_path: Union[str, Path],
    sheet_name: str = 'Sheet1',
    **kwargs
) -> None:
    """
    将DataFrame写入Excel的简单接口。

    参数:
        df: 要写入的DataFrame
        file_path: Excel文件的路径
        sheet_name: 工作表名称
        **kwargs: ExcelWriter.write_dataframe的其他参数

    示例:
        >>> write_excel(df, 'output.xlsx', sheet_name='Data')
        >>> write_excel(df, 'output.xlsx', index=False, header=True)
    """
    writer = ExcelWriter(file_path)
    writer.write_dataframe(df, sheet_name, **kwargs)


def read_multiple_excel_sheets(
    file_path: Union[str, Path],
    sheets: Optional[List[Union[str, int]]] = None,
    **kwargs
) -> Dict[str, pd.DataFrame]:
    """
    从Excel文件中读取多个工作表。

    参数:
        file_path: Excel文件的路径
        sheets: 工作表名称/索引列表(None表示全部)
        **kwargs: 读取的其他参数

    返回:
        工作表名称到DataFrame的字典映射

    示例:
        >>> data = read_multiple_excel_sheets('data.xlsx')
        >>> data = read_multiple_excel_sheets('data.xlsx', sheets=['Sheet1', 'Sheet2'])
    """
    reader = ExcelReader(file_path)
    return reader.read_multiple_sheets(sheets, **kwargs)


def write_multiple_excel_sheets(
    data_dict: Dict[str, pd.DataFrame],
    file_path: Union[str, Path],
    **kwargs
) -> None:
    """
    将多个DataFrame写入一个Excel文件的不同工作表。

    参数:
        data_dict: 工作表名称到DataFrame的字典映射
        file_path: Excel文件的路径
        **kwargs: 写入的其他参数

    示例:
        >>> data = {'Sheet1': df1, 'Sheet2': df2}
        >>> write_multiple_excel_sheets(data, 'output.xlsx')
    """
    writer = ExcelWriter(file_path)
    writer.write_multiple_sheets(data_dict, **kwargs)


def clean_excel_data(
    df: pd.DataFrame,
    **kwargs
) -> pd.DataFrame:
    """
    使用常见预处理步骤清理Excel数据。

    参数:
        df: 要清理的DataFrame
        **kwargs: ExcelDataProcessor.clean_dataframe的其他参数

    返回:
        清理后的DataFrame

    示例:
        >>> df_clean = clean_excel_data(df, remove_empty_rows=True, strip_strings=True)
    """
    return ExcelDataProcessor.clean_dataframe(df, **kwargs)


def get_excel_info(file_path: Union[str, Path]) -> Dict[str, Any]:
    """
    获取Excel文件的信息。

    参数:
        file_path: Excel文件的路径

    返回:
        包含文件信息的字典

    示例:
        >>> info = get_excel_info('data.xlsx')
        >>> print(f"文件有 {info['sheet_count']} 个工作表")
    """
    return ExcelUtilities.get_file_info(file_path)


# 示例用法和测试函数
def _create_sample_data() -> pd.DataFrame:
    """创建用于测试的示例数据。"""
    return pd.DataFrame({
        '股票代码': ['000001.SZ', '000002.SZ', '600000.SH'],
        '股票名称': ['平安银行', '万科A', '浦发银行'],
        '价格': [10.5, 25.3, 8.9],
        '成交量': [1000000, 2000000, 1500000],
        '日期': pd.date_range('2025-01-01', periods=3)
    })


def example_usage():
    """
    演示Excel工具模块的使用方法。

    此函数展示了使用Excel工具进行读取、写入和处理Excel文件的各种方法。
    """
    print("Excel工具模块 - 使用示例")
    print("=" * 50)

    # 创建示例数据
    sample_df = _create_sample_data()
    print("已创建示例数据:")
    print(sample_df)
    print()

    # 示例1: 简单的写入和读取
    print("1. 简单的写入和读取操作:")
    file_path = "example_output.xlsx"

    # 写入数据
    write_excel(sample_df, file_path, sheet_name='股票数据', index=False)
    print(f"数据已写入 {file_path}")

    # 读取数据
    df_read = read_excel(file_path, sheet='股票数据')
    print("读取的数据:")
    print(df_read.head())
    print()

    # 示例2: 多工作表操作
    print("2. 多工作表操作:")
    data_dict = {
        '股票数据': sample_df,
        '统计信息': sample_df.describe(),
        '汇总': sample_df.groupby('股票代码').sum()
    }

    multi_file = "multi_sheet_example.xlsx"
    write_multiple_excel_sheets(data_dict, multi_file)
    print(f"多个工作表已写入 {multi_file}")

    # 读取多个工作表
    all_sheets = read_multiple_excel_sheets(multi_file)
    print(f"读取了 {len(all_sheets)} 个工作表: {list(all_sheets.keys())}")
    print()

    # 示例3: 数据清理
    print("3. 数据清理操作:")
    # 创建混乱的数据
    messy_df = sample_df.copy()
    messy_df.loc[1, '股票名称'] = '  万科A  '  # 添加空白字符
    messy_df.loc[2, '价格'] = None  # 添加缺失值

    print("混乱的数据:")
    print(messy_df)

    # 清理数据
    clean_df = clean_excel_data(messy_df, strip_strings=True, remove_empty_rows=False)
    print("清理后的数据:")
    print(clean_df)
    print()

    # 示例4: 文件信息
    print("4. 文件信息:")
    info = get_excel_info(file_path)
    print(f"{file_path} 的文件信息:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    print()

    # 示例5: 高级读取选项
    print("5. 高级读取选项:")
    # 读取特定列
    df_subset = read_excel(file_path, sheet='股票数据', usecols=['股票代码', '价格'])
    print("读取特定列:")
    print(df_subset)
    print()

    # 指定数据类型读取
    dtype_mapping = {'股票代码': 'str', '价格': 'float'}
    df_typed = read_excel(file_path, sheet='股票数据', dtype=dtype_mapping)
    print("读取后的数据类型:")
    print(df_typed.dtypes)
    print()

    print("示例使用完成!")


if __name__ == "__main__":
    # 当脚本直接执行时运行示例用法
    example_usage()
